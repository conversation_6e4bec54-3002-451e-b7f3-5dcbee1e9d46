# Blood Pressure Metrics - Cardiowell R Analytics System

## Total Metrics Count: **52 Distinct Metrics**

---

## 1. Basic Statistical Metrics (12 metrics)

### Primary Measurements
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **Mean SBP** | Average systolic blood pressure | `mean(sbp_readings)` |
| **Mean DBP** | Average diastolic blood pressure | `mean(dbp_readings)` |
| **Mean Pulse** | Average pulse rate | `mean(pulse_readings)` |
| **MAP** | Mean arterial pressure | `DBP + (SBP - DBP) / 3` |

### Variability Metrics
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **SBP Standard Deviation** | Variability in systolic readings | `sd(sbp_readings)` |
| **DBP Standard Deviation** | Variability in diastolic readings | `sd(dbp_readings)` |
| **Pulse Standard Deviation** | Variability in pulse readings | `sd(pulse_readings)` |
| **Pulse Pressure SD** | Variability in pulse pressure | `sd(sbp - dbp)` |

### Coefficient of Variation
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **CV SBP** | Coefficient of variation for SBP | `(SBP_SD / Mean_SBP) × 100` |
| **CV DBP** | Coefficient of variation for DBP | `(DBP_SD / Mean_DBP) × 100` |
| **CV Pulse** | Coefficient of variation for pulse | `(Pulse_SD / Mean_Pulse) × 100` |

### Range Statistics (6 metrics)
| Metric | Description |
|--------|-------------|
| **SBP Min** | Minimum systolic reading |
| **SBP Max** | Maximum systolic reading |
| **SBP Range** | Difference between max and min SBP |
| **DBP Min** | Minimum diastolic reading |
| **DBP Max** | Maximum diastolic reading |
| **DBP Range** | Difference between max and min DBP |

---

## 2. Pressure Statistics (2 metrics)

| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **Pulse Pressure** | Difference between systolic and diastolic | `Mean_SBP - Mean_DBP` |
| **Mean Pulse Pressure** | Average pulse pressure across all readings | `mean(sbp - dbp)` |

---

## 3. Blood Pressure Classification Metrics (11 metrics)

### Hypertension Stage Classifications
| Stage | SBP Range | DBP Range | Description |
|-------|-----------|-----------|-------------|
| **Normal** | < 120 AND < 80 | Optimal blood pressure |
| **Elevated** | 120-129 AND < 80 | Pre-hypertension |
| **Stage 1 Hypertension** | 130-139 OR 80-89 | Mild hypertension |
| **Stage 2 Hypertension** | ≥ 140 OR ≥ 90 | Moderate hypertension |
| **Hypertensive Crisis** | ≥ 180 OR ≥ 120 | Severe hypertension |
| **Hypotensive** | < 90 OR < 60 | Low blood pressure |
| **Unknown** | Invalid/missing data | Unclassifiable readings |

### Control Metrics (4 metrics)
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **TIR%** | Time in Range percentage | `(readings_in_range / total_readings) × 100` |
| **TAR%** | Time Above Range percentage | `(readings_above_range / total_readings) × 100` |
| **Systolic Load%** | Percentage with SBP ≥ 130 | `(readings_sbp_≥130 / total_readings) × 100` |
| **Diastolic Load%** | Percentage with DBP ≥ 80 | `(readings_dbp_≥80 / total_readings) × 100` |

*Note: TIR uses < 130 SBP AND < 80 DBP as "in range"*

---

## 4. Time-of-Day Analysis (8 metrics)

### Time Period Definitions
| Time Period | Hours | Metrics Computed |
|-------------|-------|------------------|
| **Morning** | 6 AM - 12 PM | Mean SBP, Mean DBP, Count |
| **Afternoon** | 12 PM - 6 PM | Mean SBP, Mean DBP, Count |
| **Evening** | 6 PM - 10 PM | Mean SBP, Mean DBP, Count |
| **Night** | 10 PM - 6 AM (22:00-05:59) | Mean SBP, Mean DBP, Count |

*Note: Night period includes hours 22, 23, 0, 1, 2, 3, 4, 5*

---

## 5. Circadian Rhythm Metrics (12 metrics)

### Circadian Patterns
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **Morning Mean SBP** | Average morning systolic | `mean(sbp_readings[6-12h])` |
| **Morning Mean DBP** | Average morning diastolic | `mean(dbp_readings[6-12h])` |
| **Day Mean SBP** | Average daytime systolic | `mean(sbp_readings[6-22h])` |
| **Day Mean DBP** | Average daytime diastolic | `mean(dbp_readings[6-22h])` |
| **Night Mean SBP** | Average nighttime systolic | `mean(sbp_readings[22-6h])` |
| **Night Mean DBP** | Average nighttime diastolic | `mean(dbp_readings[22-6h])` |
| **Night Min SBP** | Minimum nighttime systolic | `min(sbp_readings[22-6h])` |
| **Morning Surge SBP** | Morning SBP - Night Min SBP | `morning_mean_sbp - night_min_sbp` |
| **Dipping Percentage SBP** | Day-night BP difference | `(day_mean_sbp - night_mean_sbp) / day_mean_sbp × 100` |
| **Morning Hypertension** | Morning BP elevation | `morning_sbp ≥ 135 OR morning_dbp ≥ 85` |
| **Absolute Nocturnal HTN** | Night hypertension | `night_sbp ≥ 120 OR night_dbp ≥ 70` |
| **Relative Nocturnal HTN** | Relative night elevation | `dipping_percentage < 10%` |

### Dipping Classification
| Classification | Dipping Percentage | Description |
|----------------|-------------------|-------------|
| **reverse** | < 0% | Night BP higher than day BP |
| **non_dipper** | 0-10% | Minimal day-night difference |
| **normal** | 10-20% | Healthy circadian pattern |
| **extreme** | > 20% | Excessive day-night difference |

---

## 6. Advanced Variability Metrics (10 metrics)

### Trend Analysis (4 metrics)
| Metric | Description | Method |
|--------|-------------|--------|
| **Overall Trend Slope SBP** | Long-term SBP trend | Linear regression over all daily means |
| **Overall Trend Slope DBP** | Long-term DBP trend | Linear regression over all daily means |
| **30-Day Trend Slope SBP** | Recent SBP trend | Linear regression over last 30 days |
| **30-Day Trend Slope DBP** | Recent DBP trend | Linear regression over last 30 days |

### Variability Measures (4 metrics)
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **DDV SBP** | Day-to-day variability SBP | `sd(daily_mean_sbp)` |
| **DDV DBP** | Day-to-day variability DBP | `sd(daily_mean_dbp)` |
| **ARV SBP** | Average real variability SBP | `mean(abs(diff(sbp_readings)))` |
| **ARV DBP** | Average real variability DBP | `mean(abs(diff(dbp_readings)))` |

### Moving Averages (2 metrics)
| Metric | Description | Formula/Calculation |
|--------|-------------|---------------------|
| **7-Day Moving Average SBP** | Rolling 7-day average of SBP | `mean(last_7_daily_sbp)` |
| **7-Day Moving Average DBP** | Rolling 7-day average of DBP | `mean(last_7_daily_dbp)` |

---

## 7. Pulse Rate Analysis (7 metrics)

### Bradycardia Detection (3 metrics)
| Threshold | Description | Count |
|-----------|-------------|-------|
| **brady_lt_40** | Severe bradycardia | Count of readings < 40 bpm |
| **brady_lt_50** | Moderate bradycardia | Count of readings < 50 bpm |
| **brady_lt_60** | Mild bradycardia | Count of readings < 60 bpm |

### Tachycardia Detection (3 metrics)
| Threshold | Description | Count |
|-----------|-------------|-------|
| **tachy_gt_100** | Mild tachycardia | Count of readings > 100 bpm |
| **tachy_gt_120** | Moderate tachycardia | Count of readings > 120 bpm |
| **tachy_gt_150** | Severe tachycardia | Count of readings > 150 bpm |

### Special Conditions (1 metric)
| Metric | Description | Criteria |
|---------|-------------|----------|
| **High BP + Low Pulse Count** | Elevated BP with bradycardia | `((SBP ≥ 130 AND DBP ≥ 80) OR (SBP ≥ 140 OR DBP ≥ 90)) AND pulse < 60` |

---

## 8. Temporal Analysis Metrics (6 metrics)

### Weekend vs Weekday Patterns
| Metric | Description | Day Definition |
|--------|-------------|----------------|
| **weekend_mean_sbp** | Average SBP on weekends | Saturday (6) and Sunday (0) |
| **weekend_mean_dbp** | Average DBP on weekends | Saturday (6) and Sunday (0) |
| **weekday_mean_sbp** | Average SBP on weekdays | Monday-Friday (1-5) |
| **weekday_mean_dbp** | Average DBP on weekdays | Monday-Friday (1-5) |
| **weekend_weekday_delta_sbp** | Weekend SBP - Weekday SBP | `weekend_mean_sbp - weekday_mean_sbp` |
| **weekend_weekday_delta_dbp** | Weekend DBP - Weekday DBP | `weekend_mean_dbp - weekday_mean_dbp` |

*Note: Day of week uses POSIX standard (0=Sunday, 1=Monday, ..., 6=Saturday)*

---

## 9. Data Quality & Context Metrics (4 metrics)

### Reading Statistics
| Metric | Description |
|--------|-------------|
| **total_readings** | Total number of valid readings |
| **computed_at** | Timestamp when analysis was computed |
| **last_input_at** | Timestamp of most recent reading |
| **analysis_version** | Version of the analysis algorithm |

---

## 10. Latest Reading Details (5 metrics)

### Most Recent Reading
| Metric | Description |
|--------|-------------|
| **Latest SBP** | Most recent systolic reading |
| **Latest DBP** | Most recent diastolic reading |
| **Latest MAP** | Most recent mean arterial pressure |
| **Latest Pulse** | Most recent pulse reading |
| **Latest Classification** | Classification of most recent reading |

---

## Summary

**Total: 52 Blood Pressure Metrics**

1. **Basic Statistical Metrics** (12)
2. **Pressure Statistics** (2)
3. **Classification Metrics** (11)
4. **Time-of-Day Analysis** (8)
5. **Circadian Rhythm Metrics** (12)
6. **Advanced Variability Metrics** (10)
7. **Pulse Rate Analysis** (7)
8. **Temporal Analysis** (6)
9. **Data Quality Metrics** (4)
10. **Latest Reading Details** (5)

---

## Implementation Notes

### Data Sources
- **Transtek Blood Pressure Devices**: Primary data source via `TranstekBloodPressureMessage` collection
- **Withings Devices**: Secondary data source via `withingsBloodPressureData` collection
- **A&D Medical Devices**: Additional data source support

### API Endpoints
- **Main Metrics**: `GET /api/bp-metrics/:imei`
- **Duration Metrics**: `GET /api/bp-duration-metrics/:imei`
- **Health Check**: `GET /api/bp-metrics/health`

### Analysis Engine
- **Primary**: R-based analytics (`Cardiowell-Analytics/bp.R`)
- **Fallback**: Node.js implementation (`nodeBpAnalysis.mjs`)
- **Feature Toggle**: Controlled via `isBPMetricsEnabledForImei()`

### Data Processing
- **Outlier Detection**: Filters invalid readings
- **Timezone Support**: Configurable timezone for circadian analysis
- **Data Validation**: Comprehensive validation of input and output
- **Caching**: Results stored in `bp_summary` collection for performance
