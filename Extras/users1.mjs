import { Router } from "express"
import sgMail from "@sendgrid/mail"
import mongoose from "mongoose"
import bcrypt from "bcrypt"
import passport from "passport"
import LocalStrategy from "passport-local"
import bodyParser from "body-parser"
import { isLoggedInAdmin } from "./isLoggedInAdmin.mjs"
import { authenticatedPatient } from "./authenticatedPatient.mjs"
import { isLoggedIn } from "./isLoggedIn.mjs"
import Patient from "../models/patient.js"
import Provider from "../models/provider.js"
import Clinic from "../models/clinic.js"
import SuperAdmin from "../models/superAdmin.js"
import {
  getPatientsForClinic,
  getPatientDataById,
  getPatientOverviewsForClinc,
} from "../users/service/patientData.mjs"
import {
  notifySms,
  iFrameNotifySms,
  notifySmsValidator,
} from "../users/controller/notifySms.mjs"
import {
  notifyEmail,
  iFrameNotifyEmail,
  notifyEmailValidator,
} from "../users/controller/notifyEmail.mjs"
import {
  sendPasswordResetLink,
  passwordResetValidator,
} from "../users/controller/sendPasswordResetLink.mjs"
import { deletePatientById } from "../users/service/deletePatientById.mjs"
import { addClinic } from "../clinic/controller/addClinic.mjs"
import { deleteClinic } from "../clinic/controller/deleteClinic.mjs"
import { patientAuthRouter } from "./patientAuth.mjs"
import {
  addPatientValidator,
  adminAddPatient,
  providerAddPatient,
  iFrameAddPatient,
} from "../users/controller/addPatient.mjs"
import {
  updatePatientValidator,
  adminSavePatientChanges,
  providerSavePatientChanges,
  patientSavePatientChanges,
  iFrameSavePatientChanges,
} from "../users/controller/editPatient.mjs"
import { getPatientRegistrationStatus } from "../users/controller/getPatient.mjs"
import { updatePatientRegistrationStatus } from "../users/controller/updatePatientRegistration.mjs"
import { getSpecificPatient } from "../users/controller/getPatient.mjs"
import {
  clinicScope,
  getTogglesMapByClinic,
  updateFeatureToggle,
} from "../feature-toggle/feature-toggle.collection.mjs"

const saltRounds = 10
sgMail.setApiKey(process.env.sendgridAPI)

export const userRouter = Router()

mongoose.connect(process.env.mongoUri, {
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useFindAndModify: false,
})
mongoose.Promise = global.Promise
const db = mongoose.connection

//passport.use('local', new LocalStrategy(Provider.authenticate()));

passport.use("local-admin", new LocalStrategy(SuperAdmin.authenticate()))

// passport.use('local-patient', new LocalStrategy(function(username, password, done) {
//   Patient.findOne({ username: username }, function(err, user) {

//     if (err) { return done(err); }
//     if (!user) { return done(null, false, { message: 'Unknown user ' + username }); }
//     bcrypt.compare(password, user.password, function(err, result) {
//       if (result === true) {
//         return done(null, user);
//       } else {
//         return done(null, false, { message: 'Invalid password' });
//       }
//   });
//   });
// }));

passport.use(
  "local",
  new LocalStrategy(function (username, password, done) {
    Provider.findOne({ username: username.toLowerCase() }, function (err, user) {
      if (err) throw err
      if (!user) {
        Patient.findOne({ username: username.toLowerCase() }, function (err, user2) {
          console.log(user2)
          if (err) {
            return done(err)
          }
          if (!user2 || !user2.password) {
            var app_user = db.collection("_User")
            app_user.findOne({ username: username.toLowerCase() }, function (err, user3) {
              if (err) {
                return done(err)
              }
              if (!user3) {
                return done(null, false, {
                  message: "Error: Unknown User",
                })
              }
              bcrypt.compare(password, user3._hashed_password, function (err, result) {
                if (err) {
                  console.log(err)
                  return done(err)
                }
                if (result !== false) {
                  // create patient acct with _User info
                  // if !user2 create an account
                  if (!user2) {
                    var data = {
                      username: user3.username.toLowerCase(),
                      firstName: user3.firstname,
                      lastName: user3.lastname,
                      email: user3.email.toLowerCase(),
                      password: user3._hashed_password,
                      bpIMEI: user3.IMEI_BPM,
                      weightIMEI: user3.IMEI_WS,
                    }
                    var patient = new Patient(data)
                    patient.save((err, model) => {
                      if (err) {
                        return done(err)
                      }
                      return done(null, model)
                    })
                  } else if (!user2.password) {
                    // else if !user2.password find and update using route code from invite
                    Patient.findByIdAndUpdate(
                      user2._id,
                      {
                        $set: {
                          password: user3._hashed_password,
                          username: user3.username.toLowerCase(),
                        },
                      },
                      function (err, doc) {
                        if (err) {
                          return done(err)
                        }
                      },
                    )
                    if (!user2.bpIMEI) {
                      Patient.findByIdAndUpdate(
                        user2._id,
                        {
                          $set: {
                            bpIMEI: user3.IMEI_BPM,
                          },
                        },
                        function (err, doc) {
                          if (err) {
                            return done(err)
                          }
                        },
                      )
                    }
                    if (!user2.weightIMEI) {
                      Patient.findByIdAndUpdate(
                        user2._id,
                        {
                          $set: {
                            weightIMEI: user3.IMEI_WS,
                          },
                        },
                        function (err, doc) {
                          if (err) {
                            return done(err)
                          }
                        },
                      )
                    }
                    return done(null, user)
                  }
                } else {
                  return done(null, false, {
                    message: "Invalid password",
                  })
                }
              })
            })
          } else {
            bcrypt.compare(password, user2.password, function (err, result) {
              if (err) {
                console.log(err)
                return done(err)
              }
              if (result !== false) {
                return done(null, user2)
              } else {
                return done(null, false, { message: "Invalid password" })
              }
            })
          }
        })
      } else {
        user.authenticate(password, function (err, result) {
          if (err) {
            console.log(err)
            return done(err)
          }
          if (result !== false) {
            return done(null, user)
          } else {
            return done(null, false, { message: "Invalid password" })
          }
        })
      }
    })
  }),
)

passport.serializeUser(function (user, done) {
  if (user.role) {
    return done(null, {
      id: user.id,
      role: user.role,
    })
  }
  // will be removed, left for BC
  return done(null, user.id)
})

passport.deserializeUser(function (user, done) {
  if (typeof user === "object") {
    if (user.role === "provider") {
      Provider.findById(user.id, function (err, provider) {
        if (err) {
          done(err)
        }
        if (provider) {
          done(null, provider)
        } else {
          done(null, false)
        }
      })
    }
    if (user.role === "superadmin") {
      SuperAdmin.findById(user.id, function (err, admin) {
        if (err) {
          done(err)
        }
        if (admin) {
          done(null, admin)
        } else {
          done(null, false)
        }
      })
    }
    if (user.role === "patient") {
      Patient.findById(user.id, function (err, patient) {
        if (err) {
          done(err)
        }
        if (patient) {
          done(null, patient)
        } else {
          done(null, false)
        }
      })
    }
    return
  }
  const id = user
  Provider.findById(id, function (err, provider) {
    if (err) {
      done(err)
    }
    if (provider) {
      done(null, provider)
    } else {
      SuperAdmin.findById(id, function (err, admin) {
        if (err) {
          done(err)
        }
        if (admin) {
          done(null, admin)
        } else {
          Patient.findById(id, function (err, patient) {
            if (err) {
              console.log(err)
              done(err)
            }
            if (patient) {
              done(null, patient)
            }
          })
        }
      })
    }
  })
})

// for authenticatedPatient:
// in userLogin, in additional to setting username in session,
// also set session.type = 'patient'

userRouter.post("/logout", function (req, res) {
  req.logOut()
  req.session.destroy(function (err) {
    if (err) {
      return res.status(500).send({ message: "Error", error: err })
    }
    return res.status(201).send({ message: "Success" })
  })
})

userRouter.post("/addClinic", isLoggedInAdmin, addClinic)

userRouter.post("/addPatient", isLoggedInAdmin, addPatientValidator, adminAddPatient)

userRouter.post(
  "/providerAddPatient",
  // isLoggedIn,
  addPatientValidator,
  providerAddPatient,
)

userRouter.post("/iFrameAddPatient", addPatientValidator, iFrameAddPatient)

userRouter.post("/addProvider", isLoggedInAdmin, function (req, res) {
  Provider.register(
    new Provider({
      username: req.body.providerUsername.toLowerCase(),
      firstName: req.body.providerFirstName,
      lastName: req.body.providerLastName,
      phoneNumber: req.body.providerPhoneNumber,
      email: req.body.providerEmail.toLowerCase(),
      clinic: req.body.providerClinic,
    }),
    req.body.password,
    function (err, model) {
      if (err) {
        return res.status(500).send({ message: "Error: " + err })
      }
      return res.status(201).send({
        message: "Success",
        provider: {
          _id: model._id,
          username: model.username,
          firstName: model.firstName,
          lastName: model.lastName,
          phoneNumber: model.phoneNumber,
          email: model.email,
          clinic: model.clinic,
        },
      })
    },
  )
})

userRouter.post("/login", passport.authenticate("local"), async function (req, res) {
  try {
    if (req.user.password) {
      req.session.username = req.user.username.toLowerCase()
      req.session.accountType = "patient"
      req.session.userRole = "patient"
      const foundPatient = await Patient.findOne({
        username: req.user.username.toLowerCase(),
      })
      return res.status(201).send({
        message: "Success",
        user: req.user.username.toLowerCase(),
        clinic: foundPatient.clinic,
        patientID: foundPatient._id,
        accountType: "patient",
      })
    } else {
      req.session.username = req.user.username
      req.session.accountType = "provider"
      req.session.userRole = "provider"
      const provider = await Provider.findOne({
        username: req.body.username.toLowerCase(),
      })
      const clinic = await Clinic.findOne({ name: provider.clinic })
      return res.status(201).send({
        message: "Success",
        accountType: "provider",
        user: provider.username.toLowerCase(),
        clinic: provider.clinic,
        providerID: provider._id,
        clinicId: clinic._id,
      })
    }
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.post("/iFrame", function (req, res) {
  Clinic.findOne({ _id: req.body.id }, async function (err, clinic) {
    if (err) {
      console.error(err)
      return res.status(500).json({ message: "Database error" })
    }

    if (!clinic) {
      return res.status(404).json({ message: "Clinic not found" })
    }

    var patientArray = await getPatientsForClinic(clinic.name)
    return res.status(201).send({
      message: "Success",
      logo: clinic.logo,
      clinic: clinic.name,
      data: patientArray,
    })
  })
})

userRouter.post("/loginAdmin", passport.authenticate("local-admin"), function (req, res) {
  req.session.adminUsername = req.body.username.toLowerCase()
  req.session.userRole = "admin"
  SuperAdmin.findOne(
    { username: req.body.username.toLowerCase() },
    function (err, admin) {
      return res.status(201).send({
        message: "Success",
        adminID: admin._id,
        session: req.session,
      })
    },
  )
})
async function allowOwnClinicAccess(req, res, next) {
  try {
    if (req.isAuthenticated() && req.user.role === "provider") {
      const provider = await Provider.findById(req.user._id)
      if (provider) {
        const clinic = await Clinic.findOne({ name: provider.clinic })
        if (clinic && String(clinic._id) === req.params.clinicId) {
          return next()
        }
      }
    }

    if (req.isAuthenticated() && req.user.role === "patient") {
      const patient = await Patient.findById(req.user._id)
      if (patient) {
        const clinic = await Clinic.findOne({ name: patient.clinic })
        if (clinic && String(clinic._id) === req.params.clinicId) {
          return next() 
        }
      }
    }

    return isLoggedInAdmin(req, res, next)
  } catch (error) {
    return isLoggedInAdmin(req, res, next)
  }
}

userRouter.get("/clinics/:clinicId", allowOwnClinicAccess, async (req, res) => {
  const clinic = await Clinic.findById(req.params.clinicId)
  if (!clinic) {
    return res.status(404).send({ message: "Clinic not found" })
  }
  const responseData = {
    ...clinic.toObject(),
    features: await getTogglesMapByClinic(clinic._id),
  }
  return res.status(200).send(responseData)
})

userRouter.get("/getAdminData", isLoggedInAdmin, async (req, res) => {
  try {
    const [clinics, providers, patients] = await Promise.all([
      Clinic.find(),
      Provider.find(),
      Patient.aggregate([
        {
          $lookup: {
            from: "withings_user_datas",
            let: {
              id: { $toString: "$_id" },
            },
            pipeline: [
              { $match: { $expr: { $eq: ["$$id", "$patientId"] } } },
              {
                $project: {
                  _id: 0,
                  deviceIds: "$devices.deviceId",
                },
              },
              { $limit: 1 },
            ],
            as: "withingsBpDevices",
          },
        },
        {
          $unwind: {
            path: "$withingsBpDevices",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            firstName: 1,
            lastName: 1,
            cellNumber: 1,
            homeNumber: 1,
            MRN: 1,
            email: 1,
            address: 1,
            city: 1,
            state: 1,
            zip: 1,
            timeZone: 1,
            bpIMEI: 1,
            ttBpIMEI: 1,
            adBpIMEI: 1,
            weightIMEI: 1,
            ttWeightIMEI: 1,
            glucoseIMEI: 1,
            pulseIMEI: 1,
            iGlucoseDeviceId: 1,
            clinic: 1,
            password: 1,
            username: 1,
            selectedBpDevice: 1,
            selectedWeightDevice: 1,
            withingsBpDevices: {
              $cond: {
                if: { $isArray: "$withingsBpDevices.deviceIds" },
                then: "$withingsBpDevices.deviceIds",
                else: [],
              },
            },
            deviceNotificationsEnabled: 1,
            targetWeight: 1,
            birthdate: 1,
            gender: 1,
            ethnicity: 1,
            maritalStatus: 1,
            education: 1,
            employment: 1,
            income: 1,
            language: 1,
            allergies: 1,
            chronicConditions: 1,
            socialConnectedness: 1,
            hypertensionMedications: 1,
            medications: 1,
            showTestData: 1,
          },
        },
      ]),
    ])

    return res.status(201).send({ message: "Success", clinics, providers, patients })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.get("/clinics", isLoggedInAdmin, async (req, res) => {
  try {
    const clinics = await Clinic.find({})
    return res.status(201).send(clinics)
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.post("/grabPatientData", isLoggedIn, async function (req, res) {
  var patientArray = await getPatientsForClinic(req.body.clinic)
  return res.status(201).send({ message: "Success", data: patientArray })
})

userRouter.post("/getClinicPatientOverviews", isLoggedIn, async (req, res) => {
  try {
    const patientArray = await getPatientOverviewsForClinc(
      req.body.clinic,
      req.body.providerId,
    )
    return res.status(201).send({ message: "Success", data: patientArray })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.post("/getPatientData", isLoggedIn, async (req, res) => {
  try {
    const foundPatient = await getPatientDataById(req.body.id, req.body.providerId)
    return res.status(201).send({ message: "Success", data: foundPatient.patientObj })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.post("/sendLink", passwordResetValidator, sendPasswordResetLink)

userRouter.post("/resetPassword", function (req, res) {
  Provider.findOne({ _id: req.body.id }, function (err, user) {
    if (err) {
      return res.status(500).send({ message: "Error: " + err })
    }
    if (user) {
      user.setPassword(req.body.password, function (err, user) {
        if (err) {
          return res.status(500).send({ message: "Error: " + err })
        }
        user.save(function (err) {
          if (err) {
            return res.status(500).send({ message: "Error: " + err })
          }
          return res.status(201).send({ message: "Success" })
        })
      })
    } else {
      bcrypt.hash(req.body.password, saltRounds, function (err, hash) {
        Patient.findByIdAndUpdate(
          req.body.id,
          {
            $set: {
              password: hash,
            },
          },
          { returnOriginal: true },
          function (err, doc) {
            if (err) {
              console.log(err)
              return res.status(500).send({ message: "Error" })
            }
            var appUser = db.collection("_User")
            appUser.findOneAndUpdate(
              { email: doc.email.toLowerCase() },
              {
                $set: {
                  _hashed_password: hash,
                },
              },
              function (err, doc2) {
                if (err) {
                  console.log(err)
                  return res.status(500).send({ message: "Error" })
                }
                return res.status(201).send({ message: "Success" })
              },
            )
          },
        )
      })
    }
  })
})

userRouter.post("/resetPassword2", isLoggedIn, function (req, res) {
  Provider.findOne({ username: req.body.username.toLowerCase() }, function (err, user) {
    if (err) {
      return res.status(500).send({ message: "Error: " + err })
    }
    user.setPassword(req.body.password, function (err, user) {
      if (err) {
        return res.status(500).send({ message: "Error: " + err })
      }
      user.save(function (err) {
        if (err) {
          return res.status(500).send({ message: "Error: " + err })
        }
        return res.status(201).send({ message: "Success" })
      })
    })
  })
})

userRouter.post("/patientResetPassword", authenticatedPatient, function (req, res) {
  bcrypt.hash(req.body.password, saltRounds, function (err, hash) {
    Patient.findByIdAndUpdate(
      req.body.id,
      {
        $set: {
          password: hash,
        },
      },
      { returnOriginal: true },
      function (err, doc) {
        if (err) {
          console.log(err)
          return res.status(500).send({ message: "Error" })
        }
        var appUser = db.collection("_User")
        appUser.findOneAndUpdate(
          { email: doc.email.toLowerCase() },
          {
            $set: {
              _hashed_password: hash,
            },
          },
          function (err, doc2) {
            if (err) {
              console.log(err)
              return res.status(500).send({ message: "Error" })
            }
            return res.status(201).send({ message: "Success" })
          },
        )
      },
    )
  })
})

userRouter.post("/notifySMS", isLoggedIn, notifySmsValidator, notifySms)

userRouter.post("/iFrameNotifySMS", notifySmsValidator, iFrameNotifySms)

userRouter.post("/notifyEmail", isLoggedIn, notifyEmailValidator, notifyEmail)

userRouter.post("/iFrameNotifyEmail", notifyEmailValidator, iFrameNotifyEmail)

userRouter.post("/deleteClinic", isLoggedInAdmin, deleteClinic)

userRouter.post("/saveClinicChanges", isLoggedInAdmin, async (req, res) => {
  try {
    const clinic = await Clinic.findByIdAndUpdate(
      req.body.id,
      {
        $set: {
          name: req.body.name,
          address: req.body.address,
          phoneNumber: req.body.phoneNumber,
          mainContact: req.body.mainContact,
        },
      },
      { new: true },
    )
    if (!clinic) {
      return res.status(404).send({ message: "Clinic not found" })
    }
    // @todo: add request validation
    const BPBuddyToggle = req.body.features?.BPBuddy?.value
    if (BPBuddyToggle) {
      await updateFeatureToggle("BPBuddy", clinicScope(String(clinic._id)), BPBuddyToggle)
    }
    const BPMetricsToggle = req.body.features?.BPMetrics?.value
    if (BPMetricsToggle) {
      await updateFeatureToggle(
        "BPMetrics",
        clinicScope(String(clinic._id)),
        BPMetricsToggle,
      )
    }

    return res.status(201).send({ message: "Success", clinic })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.post(
  "/upload-clinic-image/:clinicId",
  bodyParser.raw({ type: ["image/jpeg", "image/png"], limit: "5mb" }),
  async (req, res) => {
    const clinicId = req.params.clinicId
    const imageBuffer = req.body
    try {
      const clinic = await Clinic.findById(clinicId)
      if (!clinic) {
        return res.status(404).json({ message: "Clinic not found" })
      }

      clinic.image = imageBuffer
      await clinic.save()

      res.status(200).json({
        message: "Image uploaded and associated with the clinic successfully",
      })
    } catch (error) {
      console.error("Error uploading the image:", error)
      res.status(500).json({ message: "Internal server error" })
    }
  },
)

userRouter.get("/clinic-images/:clinicName", async (req, res) => {
  const clinicName = req.params.clinicName

  try {
    const clinic = await Clinic.findOne({ name: clinicName })

    if (!clinic) {
      res.status(404).send("Clinic not found")
      return
    }

    const clinicImage = clinic.image

    if (clinicImage && clinicImage.buffer) {
      // Convert the binary data to a Base64 string
      const imageBase64 = Buffer.from(clinicImage.buffer).toString("base64")
      res.json({ image: imageBase64 })
    } else {
      // If the image field is empty, return a 404 Not Found response
      res.status(404).send("Image not found")
    }
  } catch (error) {
    // Handle any errors that occur during the database query
    console.error(error)
    res.status(500).send("Internal Server Error")
  }
})

userRouter.post("/deleteProvider", isLoggedInAdmin, async (req, res) => {
  try {
    const provider = await Provider.findByIdAndDelete(req.body.id)
    if (!provider) {
      return res.status(404).send({ message: "Provider not found" })
    }
    return res.status(201).send({ message: "Success", provider })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.post("/saveProviderChanges", isLoggedInAdmin, async (req, res) => {
  try {
    const provider = await Provider.findByIdAndUpdate(
      req.body.id,
      {
        $set: {
          username: req.body.username.toLowerCase(),
          firstName: req.body.firstName,
          phoneNumber: req.body.phoneNumber,
          lastName: req.body.lastName,
          email: req.body.email.toLowerCase(),
          clinic: req.body.clinic,
        },
      },
      { new: true },
    )
    if (!provider) {
      return res.status(404).send({ message: "Provider not found" })
    }
    return res.status(201).send({ message: "Success", provider })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.post("/deletePatient", isLoggedInAdmin, async (req, res) => {
  try {
    const patient = await deletePatientById(req.body.id)
    if (!patient) {
      return res.status(404).send({ message: "Patient not found" })
    }
    return res.status(201).send({ message: "Success", patient })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.post(
  "/savePatientChanges",
  isLoggedInAdmin,
  updatePatientValidator,
  adminSavePatientChanges,
)

userRouter.post("/providerDeletePatient", isLoggedIn, async (req, res) => {
  try {
    const patient = await deletePatientById(req.body.id)
    if (patient) {
      return res.status(201).send({ message: "Success", patient })
    }
    return res.status(404).send({ message: "Patient not found", patient })
  } catch (err) {
    console.error(err)
    return res.status(500).json(err)
  }
})

userRouter.post("/iFrameDeletePatient", async function (req, res) {
  deletePatientById(req.body.id)
  var patientArray = await getPatientsForClinic(req.body.patientClinic)
  return res.status(201).send({ message: "Success", data: patientArray })
})

userRouter.post(
  "/patientSavePatientChanges",
  authenticatedPatient,
  updatePatientValidator,
  patientSavePatientChanges,
)

userRouter.get("/getPatient/:imei", isLoggedIn, getSpecificPatient)

userRouter.post(
  "/providerSavePatientChanges",
  isLoggedIn,
  updatePatientValidator,
  providerSavePatientChanges,
)

userRouter.post(
  "/iFrameSavePatientChanges",
  updatePatientValidator,
  iFrameSavePatientChanges,
)

userRouter.post("/invitePatient", isLoggedIn, function (req, res) {
  var id = req.body.id
  Patient.findById(id, function (err, patient) {
    if (err) {
      console.log(err)
      return res.status(500).send({ message: "Error" })
    }
    var email = patient.email.toLowerCase()
    var collection = db.collection("_User")
    collection.findOne({ email: email.toLowerCase() }, async function (err, result) {
      if (result) {
        if (patient.bpIMEI.length > 0) {
          collection.findOneAndUpdate(
            { email: email.toLowerCase() },
            {
              $set: {
                IMEI_BPM: patient.bpIMEI,
              },
            },
            function (err, doc) {
              if (err) {
                console.log(err)
                return res.status(500).send({ message: "Error" })
              }
            },
          )
        } else if (result.IMEI_BPM) {
          Patient.findByIdAndUpdate(
            id,
            {
              $set: {
                bpIMEI: result.IMEI_BPM,
              },
            },
            function (err, doc) {
              if (err) {
                console.log(err)
                return res.status(500).send({ message: "Error" })
              }
            },
          )
        }
        if (patient.weightIMEI.length > 0) {
          collection.findOneAndUpdate(
            { email: email.toLowerCase() },
            {
              $set: {
                IMEI_WS: patient.weightIMEI,
              },
            },
            function (err, doc) {
              if (err) {
                console.log(err)
                return res.status(500).send({ message: "Error" })
              }
            },
          )
        } else if (result.IMEI_WS) {
          Patient.findByIdAndUpdate(
            id,
            {
              $set: {
                weightIMEI: result.IMEI_WS,
              },
            },
            function (err, doc) {
              if (err) {
                console.log(err)
                return res.status(500).send({ message: "Error" })
              }
            },
          )
        }
        Patient.findByIdAndUpdate(
          id,
          {
            $set: {
              password: result._hashed_password,
              username: result.username.toLowerCase(),
            },
          },
          function (err, doc) {
            if (err) {
              console.log(err)
              return res.status(500).send({ message: "Error" })
            }
          },
        )
        // Email code here
        const msg = {
          to: email.toLowerCase(),
          from: `${process.env.FROM_EMAIL}`,
          subject: "Invitation to use CardioWell",
          html: `<div><h4>Welcome!</h4><p>Your medical provider has invited you to use the CardioWell Care Portal. Please visit <a href="${process.env.BASE_URL}" target="_blank">${process.env.BASE_URL}</a> and login using the same email/password as the cardiowell mobile application.</p><br></div>`,
        }
        sgMail.send(msg, function (err, dope) {
          if (err) {
            console.log(err.response.body.errors)
            return res.status(500).send({ message: "Error" })
          }
          return res.status(201).send({ message: "Success" })
        })
      } else {
        // Invite patient email
        // Link to a page to create username+password
        const msg = {
          to: email.toLowerCase(),
          from: `${process.env.FROM_EMAIL}`,
          subject: "Invitation to use CardioWell",
          html: `<div><h4>Welcome!</h4><p>Your medical provider has invited you to use the CardioWell Care Portal. Please visit <a href="${process.env.BASE_URL}/createAccount/${id}" target="_blank">${process.env.BASE_URL}/createAccount/${id}</a> and create an account.</p><br></div>`,
        }
        sgMail.send(msg, (err, result) => {
          if (err) {
            console.error(err.response.body.errors)
            return res.status(500).json(err.response.body.errors)
          }
          return res.status(201).send({ message: "Success" })
        })
        // Create new route to save user
      }
    })
  })
})

userRouter.post("/createAccount", function (req, res) {
  bcrypt.hash(req.body.password, saltRounds, function (err, hash) {
    Patient.findByIdAndUpdate(
      req.body.id,
      {
        $set: {
          password: hash,
          username: req.body.username.toLowerCase(),
        },
      },
      function (err, doc) {
        if (err) {
          console.log(err)
          return res.status(500).send({ message: "Error" })
        }
        var appUser = db.collection("_User")
        appUser.save(
          {
            username: req.body.username.toLowerCase(),
            firstname: doc.firstName,
            lastname: doc.lastName,
            email: doc.email.toLowerCase(),
            _hashed_password: hash,
            IMEI_BPM: doc.bpIMEI,
            IMEI_WS: doc.weightIMEI,
          },
          function (err, doc2) {
            if (err) return handleError(err) // eslint-disable-line no-undef
            return res.status(201).send({ message: "Success" })
          },
        )
      },
    )
  })
})

// Patient registration status routes
userRouter.get("/patient/:patientId/registration-status", getPatientRegistrationStatus)
userRouter.put("/patient/:patientId/registration-status", updatePatientRegistrationStatus)

userRouter.use("/auth", patientAuthRouter)
