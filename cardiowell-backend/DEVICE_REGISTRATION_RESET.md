# Device Registration Reset Feature

## Overview

A development-only feature that allows QA testers and developers to reset device registration status without requiring direct database access. This enables efficient testing of the complete registration workflow.

## Problem Statement

During testing, devices become registered and cannot be re-registered without manual database intervention. QA testers need the ability to reset registration state to:
- Test the full registration flow repeatedly
- Automate registration testing
- Avoid "burning" IMEI numbers for each test
- Work independently without developer assistance

## Solution

A reset endpoint and UI button that clears registration data while preserving the device-patient linkage.

---

## What Gets Reset

### Backend (MongoDB)

**Fields Set to False:**
- `deviceNotificationsEnabled` → `false`
- `registrationCompleted` → `false`

**Fields Removed:**
- `cellNumber` → deleted
- `firstName` → deleted
- `lastName` → deleted

**Fields Preserved:**
- `bpIMEI` or `ttBpIMEI` → **kept intact** (maintains device-patient linkage)
- `clinic` → unchanged
- `programId` → unchanged
- All other patient data → unchanged

### Frontend (Browser localStorage)

**Cleared:**
- `patientFormCompleted` - patient form completion status
- `avatar_{imei}` - patient avatar for specific IMEI

---

## How It Works

### 1. API Endpoint

**URL:** `POST /routes/device-updates/{imei}/reset`

**Environment:** Development only (`NODE_ENV === "development"`)

**Authentication:** None required (dev-only safety through environment check)

**Example:**
```bash
curl -X POST "http://localhost:8081/routes/device-updates/866833044568233/reset"
```

**Success Response (200):**
```json
{
  "message": "Device registration reset successfully",
  "imei": "866833044568233",
  "patient": {
    "id": "6644d67b6161cf0032ba4e90",
    "bpIMEI": "866833044568233",
    "ttBpIMEI": "",
    "deviceNotificationsEnabled": false,
    "registrationCompleted": false
  }
}
```

**Error Responses:**
- `403` - Production environment (endpoint disabled)
- `404` - IMEI not found in database
- `400` - Invalid IMEI format

### 2. UI Button

**Location:** Device registration page (`/device-updates/register/{imei}`)

**Visibility:** Only in development environment

**Appearance:** Red outlined button labeled "Reset Registration (DEV)"

**Behavior:**
1. Calls reset API endpoint
2. Clears localStorage data
3. Shows success message
4. Automatically refreshes page after 2 seconds

---

## Usage Workflow

### For QA Testing

**Step 1: Reset the Device**
- Navigate to: `https://careportal.cardiowell.com/device-updates/register/{imei}`
- Click the red "Reset Registration (DEV)" button
- Wait for success message and auto-refresh

**Step 2: Register the Device**
- Fill out registration form with test data
- Submit registration
- Verify registration success

**Step 3: Repeat**
- Go back to registration page
- Click reset button again
- Test registration workflow again

### Automated Testing

```bash
# Reset device
curl -X POST "http://localhost:8081/routes/device-updates/{imei}/reset"

# Verify reset
curl -X GET "http://localhost:8081/routes/device-updates/status/{imei}"
# Expected: {"message":"Found","activated":false}

# Register device
curl -X POST "http://localhost:8081/routes/device-updates/register" \
  -H "Content-Type: application/json" \
  -d '{"imei":"866833044568233","firstName":"Test","lastName":"User","cellNumber":"+15551234567"}'

# Verify registration
curl -X GET "http://localhost:8081/routes/device-updates/status/{imei}"
# Expected: {"message":"Found","activated":true}
```

---

## Security

### Environment Protection
- Endpoint is **completely disabled** in production
- Production attempts return `403 Forbidden`
- UI button is **never rendered** in production

### Why This Is Safe
1. Environment check on both frontend and backend
2. No authentication bypass - patient record must exist
3. Preserves IMEI linkage - doesn't orphan devices
4. Only affects registration state - doesn't delete patient records

---

## Technical Details

### Patient Record Lifecycle

**Pre-Registration (Initial State):**
```javascript
{
  bpIMEI: "866833044568233",
  clinic: "Cardiowell Clinic",
  deviceNotificationsEnabled: false,
  registrationCompleted: false,
  cellNumber: undefined,
  firstName: undefined,
  lastName: undefined
}
```

**After Registration:**
```javascript
{
  bpIMEI: "866833044568233",
  clinic: "Cardiowell Clinic",
  deviceNotificationsEnabled: true,
  registrationCompleted: false,
  cellNumber: "+15551234567",
  firstName: "John",
  lastName: "Doe"
}
```

**After Form Completion:**
```javascript
{
  bpIMEI: "866833044568233",
  clinic: "Cardiowell Clinic",
  deviceNotificationsEnabled: true,
  registrationCompleted: true,  // ← Now true
  cellNumber: "+15551234567",
  firstName: "John",
  lastName: "Doe"
}
```

**After Reset (Back to Initial State):**
```javascript
{
  bpIMEI: "866833044568233",  // ← Preserved
  clinic: "Cardiowell Clinic",  // ← Preserved
  deviceNotificationsEnabled: false,  // ← Reset
  registrationCompleted: false,  // ← Reset
  cellNumber: undefined,  // ← Removed
  firstName: undefined,  // ← Removed
  lastName: undefined  // ← Removed
}
```

### Registration Status Check

The system considers a device "activated" when:
```javascript
activated = deviceNotificationsEnabled === true && cellNumber !== null
```

This means:
- **Not Activated:** Shows registration form
- **Activated:** Redirects to dashboard

---

## Files Modified

### Backend
- `/deviceUpdates/controller/resetDeviceRegistration.mjs` - Reset logic
- `/routes/deviceUpdates.mjs` - Route configuration

### Frontend
- `/components/DeviceNotifications/DeviceRegister/DeviceSignUpPage.jsx` - UI button and reset handler
- `/components/DeviceNotifications/resetDeviceRegistration.js` - API call wrapper

---

## FAQs

**Q: Why not just delete the patient record?**  
A: Patient records contain important clinic and program associations. Deleting would break these linkages and require re-creating the entire patient setup.

**Q: Can this be used in production?**  
A: No. The endpoint returns 403 Forbidden in production, and the UI button is never rendered.

**Q: What if I need to reset in production?**  
A: Contact a developer with database access to manually update the patient record.

**Q: Does this affect historical data?**  
A: No. This only affects registration status. Measurement data, clinical notes, and other patient information remain intact.

**Q: Can I reset a device that was never registered?**  
A: Yes. The reset operation is idempotent - it safely handles devices in any state.

---

## Support

For issues or questions:
1. Check that `NODE_ENV === "development"`
2. Verify IMEI exists in database
3. Check browser console for errors
4. Review backend logs for API errors

