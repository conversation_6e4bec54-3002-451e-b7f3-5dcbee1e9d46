import { saveAdData } from "./saveAdData.mjs"
import { forwardMessage, FORWARD_STATUS_NOT_SENT } from "../forwardMessage.mjs"
import { addForwardTime } from "./addForwardTime.mjs"
import { getLastDeviceMeasurement } from "./getLastDeviceMeasurement.mjs"
import { emitNewBPReading } from "../../../scripts/r-bp/bpEventSystem.mjs"

const isActiveMode = process.env.AD_PROCESSING_MODE === "active"

export const handleMessage = async (payload, device, isTest) => {
  let newMessage
  // If payload contains a 'measurements' array, extract the first element
  if (payload && Array.isArray(payload.measurements) && payload.measurements.length > 0) {
    payload = payload.measurements[0]
  }
  if (isActiveMode && device) {
    newMessage = await getLastDeviceMeasurement(device)
    await saveAdData(newMessage, device?._id.toString(), isTest)
    console.log(`Handle A&D message ${newMessage} in active mode`)
  } else {
    newMessage = device
      ? await saveAdData(payload, device?._id.toString(), isTest)
      : await saveAdData(payload, null, isTest) // Just save message if no device
  }

  if (payload?.imei) {
    emitNewBPReading(payload.imei, "ad", "ad_bpms", payload.timestamp)
  }

  if (device?.customer) {
    const { endpoints = [], testReport } = device.customer
    const endpoint = endpoints.find(({ _id }) => _id.toString() === device.endpoint)

    if (endpoint) {
      let forwardResults
      if (endpoint.forward) {
        newMessage = await addForwardTime(newMessage)
        forwardResults = await forwardMessage({ endpoint, message: payload })
      } else {
        forwardResults = { status: FORWARD_STATUS_NOT_SENT }
      }

      /* eslint-disable no-undef */
      if (testReport) {
        await TestMessage.create({
          imei,
          testReport,
          message: payload,
          forwardResults,
        })
      }
      /* eslint-enable no-undef */
    }
  }

  // updateBerryLatestMessages(imei, newMessage);
}
