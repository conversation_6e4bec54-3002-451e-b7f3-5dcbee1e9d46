import { BodyTraceMessage } from "../../../models/bodyTraceMessage.mjs"
import { updateBodyTraceLatestMessages } from "./bodyTraceLatestMessages.mjs"
import { isBodyTraceBloodPressureMessage } from "./isBodyTraceBloodPressureMessage.mjs"
import { isBodyTraceWeightMessage } from "./isBodyTraceWeightMessage.mjs"
import { isBodyTraceHeartbeatMessage } from "./isBodyTraceHeartbeatMessage.mjs"
import { ServiceError } from "../ServiceError.mjs"
import { getDeviceWithCustomer } from "../getDeviceWithCustomer.mjs"
import { forwardMessage, FORWARD_STATUS_NOT_SENT } from "../forwardMessage.mjs"
import { addForwardTime } from "./addForwardTime.mjs"
import { emitNewBPReading } from "../../../scripts/r-bp/bpEventSystem.mjs"

export const BODY_TRACE_UNKNOWN_MESSAGE_TYPE_ERROR =
  "BODY_TRACE_UNKNOWN_MESSAGE_TYPE_ERROR"

export const handleBodyTraceMessage = async message => {
  if (
    isBodyTraceBloodPressureMessage(message) ||
    isBodyTraceWeightMessage(message) ||
    isBodyTraceHeartbeatMessage(message)
  ) {
    const imei = message?.imei
    let newMessage = await BodyTraceMessage.create({ message })

    if (imei) {
      const device = await getDeviceWithCustomer(imei)

      if (device?.customer) {
        const { endpoints = [], testReport } = device.customer
        const endpoint = endpoints.find(({ _id }) => _id.toString() === device.endpoint)

        if (endpoint) {
          let forwardResults
          if (endpoint.forward) {
            newMessage = await addForwardTime(newMessage)
            forwardResults = await forwardMessage({ endpoint, message })
          } else {
            forwardResults = { status: FORWARD_STATUS_NOT_SENT }
          }

          /* eslint-disable no-undef */
          if (testReport) {
            await TestMessage.create({
              imei,
              testReport,
              message,
              forwardResults,
            })
          }
          /* eslint-enable no-undef */
        }
      }

      if (isBodyTraceBloodPressureMessage(message)) {
        emitNewBPReading(imei, "bodytrace", "bodytracemessages", message.ts)
      }
    }

    updateBodyTraceLatestMessages(imei, newMessage)
  } else {
    throw new ServiceError("Unknown message type", BODY_TRACE_UNKNOWN_MESSAGE_TYPE_ERROR)
  }
}
