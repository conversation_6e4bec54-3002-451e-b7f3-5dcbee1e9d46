import { TranstekBloodPressureStatusMessage } from "../../../models/transtekBloodPressureMessage.mjs"
import { TestMessage } from "../../../models/customer.mjs"
import { saveBloodPressureData } from "../saveBloodPressureData.mjs"
import { getDeviceWithCustomer } from "../getDeviceWithCustomer.mjs"
import { forwardMessage, FORWARD_STATUS_NOT_SENT } from "../forwardMessage.mjs"
import { updateTranstekBloodPressureLatestMessages } from "../../service/transtek/transtekBloodPressureLatestMessages.mjs"
import { addForwardTime } from "./bpm/addForwardTime.mjs"
import { emitNewBPReading } from "../../../scripts/r-bp/bpEventSystem.mjs"

export const handleSphygmomanometerMessage = async (message, isTelemetry) => {
  try {
    let newMessage
    let imei
    if (isTelemetry) {
      newMessage = await saveBloodPressureData(message)
      imei = message?.data?.imei
    } else {
      newMessage = await TranstekBloodPressureStatusMessage.create({ message })
      imei = message?.status?.imei
    }

    if (imei) {
      const device = await getDeviceWithCustomer(imei)

      if (device?.customer) {
        const { endpoints = [], testReport } = device.customer
        const endpoint = endpoints.find(({ _id }) => _id.toString() === device.endpoint)

        if (endpoint) {
          let forwardResults
          if (endpoint.forward) {
            newMessage = await addForwardTime(newMessage)
            forwardResults = await forwardMessage({ endpoint, message })
          } else {
            forwardResults = { status: FORWARD_STATUS_NOT_SENT }
          }

          if (testReport) {
            await TestMessage.create({
              imei,
              testReport,
              message,
              forwardResults,
            })
          }
        }
      }

      if (isTelemetry) {
        emitNewBPReading(imei, "transtek", "Transtek_BPM", message?.data?.ts)
      }
    }

    await updateTranstekBloodPressureLatestMessages(imei, newMessage)
  } catch (err) {
    console.error(err)
  }
}
