import { param, validationResult } from "express-validator"
import { getPatientFromImei } from "../service/getPatientFromImei.mjs"

export const resetDeviceRegistrationValidator = [param("imei").isIMEI().escape()]

/**
 * Dev-only endpoint to reset device registration for testing purposes
 * This allows restarting the registration process from the beginning
 */
export const resetDeviceRegistration = async (req, res) => {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV !== "development") {
      return res.status(403).send({
        error: "forbidden",
        message: "This endpoint is only available in development environment",
      })
    }

    validationResult(req).throw()
    const { imei } = req.params

    const patient = await getPatientFromImei(imei)

    if (!patient) {
      return res.status(404).send({
        error: "imei-account-not-found",
        message: "This IMEI does not exist in any account in our records",
        imei,
      })
    }

    // Reset registration fields while keeping IMEI connection
    patient.deviceNotificationsEnabled = false
    patient.registrationCompleted = false

    // Unset these fields (remove them from the document)
    patient.cellNumber = undefined
    patient.firstName = undefined
    patient.lastName = undefined

    await patient.save()

    return res.status(200).send({
      message: "Device registration reset successfully",
      imei,
      patient: {
        id: patient._id,
        bpIMEI: patient.bpIMEI,
        ttBpIMEI: patient.ttBpIMEI,
        deviceNotificationsEnabled: patient.deviceNotificationsEnabled,
        registrationCompleted: patient.registrationCompleted,
      },
    })
  } catch (error) {
    console.error("Error resetting device registration:", error)
    return res.status(400).json(error)
  }
}
