import { findToggleByScope, clinicScope } from "./feature-toggle.collection.mjs"
import { Device } from "../models/device.mjs"
import Patient from "../models/patient.js"
import Clinic from "../models/clinic.js"

export const isBPMetricsEnabledForImei = async imei => {
  if (process.env.NODE_ENV !== "development") {
    return false
  }

  const device = await Device.findOne({ imei }).lean().exec()

  let clinicId = device?.clinic
  if (!clinicId) {
    const patient = await Patient.findOne({
      $or: [{ bpIMEI: imei }, { ttBpIMEI: imei }, { adBpIMEI: imei }],
    })
      .lean()
      .exec()
    clinicId = patient?.clinic || null
  }

  let resolvedClinicId = clinicId
  const isLikelyObjectId = /^[a-f\d]{24}$/i.test(String(clinicId || ""))
  if (clinicId && !isLikelyObjectId) {
    const clinicDoc = await Clinic.findOne({ name: clinicId }).lean().exec()
    if (clinicDoc?._id) {
      resolvedClinicId = String(clinicDoc._id)
    }
  }

  console.log("[FeatureToggle] isBPMetricsEnabledForImei", {
    imei,
    deviceClinic: device?.clinic,
    resolvedClinic: resolvedClinicId,
  })

  if (!resolvedClinicId) {
    return false
  }
  const toggle = await findToggleByScope("BPMetrics", clinicScope(resolvedClinicId))
  console.log("[FeatureToggle] toggle result", {
    clinicId: resolvedClinicId,
    toggleValue: toggle?.value,
  })
  if (!toggle) return false
  return toggle.value === "enabled"
}
