import { FeatureToggle } from "./models/feature-toggle.mjs"
import mongoose from "mongoose"

export const clinicScope = clinicId => {
  return {
    scope: "clinic",
    scopeId: String(clinicId),
  }
}

export const patientScope = patientId => {
  return {
    scope: "patient",
    scopeId: String(patientId),
  }
}

export const findTogglesByScopes = async (feature, scopes) => {
  const toggles = await FeatureToggle.find({
    $or: scopes.map(scope => ({
      feature: feature,
      scope: scope.scope,
      scopeId: scope.scopeId,
    })),
  })
  const togglesMap = toggles.reduce((acc, toggle) => {
    acc[`${toggle.scope}-${toggle.scopeId}`] = toggle
    return acc
  }, {})
  return scopes.map(scope => {
    return togglesMap[`${scope.scope}-${scope.scopeId}`] ?? null
  })
}

export const findToggleByScope = async (feature, scope) => {
  const scopeIdStr = String(scope.scopeId)
  const variants = [scopeIdStr]
  if (mongoose.Types.ObjectId.isValid(scopeIdStr)) {
    variants.push(new mongoose.Types.ObjectId(scopeIdStr))
  }
  return await FeatureToggle.findOne({
    feature: feature,
    scope: scope.scope,
    scopeId: { $in: variants },
  })
}

export const updateFeatureToggle = async (feature, scope, value) => {
  return await FeatureToggle.findOneAndUpdate(
    {
      feature: feature,
      scope: scope.scope,
      scopeId: scope.scopeId,
    },
    {
      value: value,
    },
    { upsert: true, new: true, setDefaultsOnInsert: true },
  )
}

export const getTogglesMapByClinic = async clinicId => {
  // const toggles = await FeatureToggle.find(clinicScope(clinicId))
  const scopeIdStr = String(clinicId)
  const variants = [scopeIdStr]
  if (mongoose.Types.ObjectId.isValid(scopeIdStr)) {
    variants.push(new mongoose.Types.ObjectId(scopeIdStr))
  }
  const toggles = await FeatureToggle.find({
    scope: "clinic",
    scopeId: { $in: variants },
  })
  const togglesMap = toggles.reduce((acc, toggle) => {
    acc[toggle.feature] = toggle
    return acc
  }, {})
  return togglesMap
}
