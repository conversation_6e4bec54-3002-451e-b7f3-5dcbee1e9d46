import { Router } from "express"
import { getDurationMetrics } from "../scripts/r-bp/metricsRetrieval.mjs"
import { isBPMetricsEnabledForImei } from "../feature-toggle/bp-metrics.mjs"
import { isAuthenticatedUser } from "./isLoggedInProvider.mjs"

const bpDurationMetricsRouter = Router()

function extractDurationMetrics(data) {
  if (!data) return null

  const durationAverages = {
    last7Days: null,
    last1Month: null,
    last3Months: null,
    last1Year: null,
    allTime: null,
  }

  const timeOfDayAverages = {
    morning: null,
    afternoon: null,
    evening: null,
    night: null,
  }

  const extremeValues = {
    maxBP: null,
    minBP: null,
  }

  if (data.metrics7Days?.basicStats) {
    const stats = data.metrics7Days.basicStats
    if (stats.mean_sbp && stats.mean_dbp) {
      durationAverages.last7Days = `${Math.round(stats.mean_sbp)}/${Math.round(stats.mean_dbp)}`
    }
  }

  if (data.metrics1Month?.basicStats) {
    const stats = data.metrics1Month.basicStats
    if (stats.mean_sbp && stats.mean_dbp) {
      durationAverages.last1Month = `${Math.round(stats.mean_sbp)}/${Math.round(stats.mean_dbp)}`
    }
  }

  if (data.metrics3Months?.basicStats) {
    const stats = data.metrics3Months.basicStats
    if (stats.mean_sbp && stats.mean_dbp) {
      durationAverages.last3Months = `${Math.round(stats.mean_sbp)}/${Math.round(stats.mean_dbp)}`
    }
  }

  if (data.metrics1Year?.basicStats) {
    const stats = data.metrics1Year.basicStats
    if (stats.mean_sbp && stats.mean_dbp) {
      durationAverages.last1Year = `${Math.round(stats.mean_sbp)}/${Math.round(stats.mean_dbp)}`
    }
  }

  const allTimeStats =
    data.metricsAll?.basicStats ||
    data.metrics1Year?.basicStats ||
    data.metrics6Months?.basicStats ||
    data.metrics3Months?.basicStats ||
    data.metrics1Month?.basicStats

  if (allTimeStats?.mean_sbp && allTimeStats?.mean_dbp) {
    durationAverages.allTime = `${Math.round(allTimeStats.mean_sbp)}/${Math.round(allTimeStats.mean_dbp)}`
  }

  const timeOfDayData =
    data.metricsAll?.timeOfDay ||
    data.metrics1Month?.timeOfDay ||
    data.metrics3Months?.timeOfDay
  if (timeOfDayData) {
    if (timeOfDayData.morning?.mean_sbp && timeOfDayData.morning?.mean_dbp) {
      timeOfDayAverages.morning = `${Math.round(timeOfDayData.morning.mean_sbp)}/${Math.round(timeOfDayData.morning.mean_dbp)}`
    }
    if (timeOfDayData.afternoon?.mean_sbp && timeOfDayData.afternoon?.mean_dbp) {
      timeOfDayAverages.afternoon = `${Math.round(timeOfDayData.afternoon.mean_sbp)}/${Math.round(timeOfDayData.afternoon.mean_dbp)}`
    }
    if (timeOfDayData.evening?.mean_sbp && timeOfDayData.evening?.mean_dbp) {
      timeOfDayAverages.evening = `${Math.round(timeOfDayData.evening.mean_sbp)}/${Math.round(timeOfDayData.evening.mean_dbp)}`
    }
    if (timeOfDayData.night?.mean_sbp && timeOfDayData.night?.mean_dbp) {
      timeOfDayAverages.night = `${Math.round(timeOfDayData.night.mean_sbp)}/${Math.round(timeOfDayData.night.mean_dbp)}`
    }
  }

  const rangeData =
    data.metricsAll?.rangeStats ||
    data.metrics1Month?.rangeStats ||
    data.metrics3Months?.rangeStats
  if (rangeData) {
    if (rangeData.sbp_max && rangeData.dbp_max) {
      extremeValues.maxBP = `${Math.round(rangeData.sbp_max)}/${Math.round(rangeData.dbp_max)}`
    }
    if (rangeData.sbp_min && rangeData.dbp_min) {
      extremeValues.minBP = `${Math.round(rangeData.sbp_min)}/${Math.round(rangeData.dbp_min)}`
    }
  }

  return {
    durationAverages,
    timeOfDayAverages,
    extremeValues,
  }
}

bpDurationMetricsRouter.get("/:imei", isAuthenticatedUser, async (req, res) => {
  try {
    const { imei } = req.params

    const enabled = await isBPMetricsEnabledForImei(imei)
    if (!enabled) {
      return res.status(403).json({
        success: false,
        message: "BP Duration Metrics feature is disabled for this clinic",
        data: null,
      })
    }

    const result = await getDurationMetrics(imei)

    if (!result.success) {
      return res.status(404).json({
        success: false,
        message: result.error || "Duration metrics not found",
        data: null,
      })
    }

    const extractedMetrics = extractDurationMetrics(result.data)

    if (!extractedMetrics) {
      return res.status(200).json({
        success: true,
        message: "No duration metrics available",
        dataAvailable: false,
        data: null,
      })
    }

    const response = {
      success: true,
      requestTime: new Date().toISOString(),
      dataAvailable: true,
      data: extractedMetrics,
    }

    return res.status(200).json(response)
  } catch (error) {
    console.error("API Error getting duration metrics:", error)
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      data: null,
    })
  }
})

export default bpDurationMetricsRouter
