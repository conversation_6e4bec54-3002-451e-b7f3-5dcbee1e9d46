import { Router } from "express"
import { fetchBPReadings } from "../scripts/r-bp/dataFetcher.mjs"
import { computeMetricsFromReadings } from "../scripts/r-bp/computeMetrics.mjs"
import { isBPMetricsEnabledForImei } from "../feature-toggle/bp-metrics.mjs"
import { isAuthenticatedUser } from "./isLoggedInProvider.mjs"

const bpMetricsRouter = Router()

bpMetricsRouter.get("/:imei", isAuthenticatedUser, async (req, res) => {
  try {
    const { imei } = req.params
    const { start, end, tz } = req.query

    const rangeStart = start || null
    const rangeEnd = end || null
    const timezone = tz || "UTC"

    const enabled = await isBPMetricsEnabledForImei(imei)
    if (!enabled) {
      return res.status(403).json({
        success: false,
        message: "BP Metrics feature is disabled for this clinic",
        data: null,
      })
    }

    const readings = await fetchBPReadings(imei, rangeStart, rangeEnd)

    const metrics = computeMetricsFromReadings(readings, {
      tz: timezone,
      rangeStart: rangeStart || null,
      rangeEnd: rangeEnd || null,
    })

    const response = {
      success: true,
      requestTime: new Date().toISOString(),
      ...metrics,
    }

    return res.status(200).json(response)
  } catch (error) {
    console.error("API Error getting duration metrics:", error)
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      data: null,
    })
  }
})

bpMetricsRouter.get("/:imei/summary", isAuthenticatedUser, async (req, res) => {
  try {
    return res.status(200).json({
      success: true,
      message: "Summary endpoint not available without cache",
      data: null,
    })
  } catch (e) {
    console.error("bp-metrics summary error:", e)
    return res
      .status(500)
      .json({ success: false, message: "Internal server error", data: null })
  }
})

bpMetricsRouter.get("/health", async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      message: "BP Metrics API is healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Health check failed",
      error: error.message,
    })
  }
})

export default bpMetricsRouter
