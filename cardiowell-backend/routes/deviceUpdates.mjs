import { Router } from "express"
import { param } from "express-validator"
import {
  registerValidator,
  registerDevice,
} from "../deviceUpdates/controller/registerDevice.mjs"
import { getDeviceStatus } from "../deviceUpdates/controller/deviceStatus.mjs"
import { getDeviceMeasures } from "../deviceUpdates/controller/deviceMeasures.mjs"
import { generateMagicLink } from "../deviceUpdates/controller/deviceMagicLink.mjs"
import {
  resetDeviceRegistrationValidator,
  resetDeviceRegistration,
} from "../deviceUpdates/controller/resetDeviceRegistration.mjs"

export const deviceUpdatesRouter = Router()

const imeiParamValidator = [param("imei").isIMEI().escape()]

deviceUpdatesRouter.route("/register").post(registerValidator, registerDevice)

deviceUpdatesRouter.route("/status/:imei").get(imeiParamValidator, getDeviceStatus)

deviceUpdatesRouter.route("/:imei").get(imeiParamValidator, getDeviceMeasures)
deviceUpdatesRouter.route("/:imei/magic-link").post(imeiParamValidator, generateMagicLink)

// Dev-only endpoint for resetting device registration
if (process.env.NODE_ENV === "development") {
  deviceUpdatesRouter
    .route("/:imei/reset")
    .post(resetDeviceRegistrationValidator, resetDeviceRegistration)
}
