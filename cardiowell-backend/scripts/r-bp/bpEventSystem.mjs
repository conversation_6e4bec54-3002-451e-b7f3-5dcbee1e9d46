import { EventEmitter } from "events"
import dotenv from "dotenv"
import { processAllDurations } from "./durationCalculator.mjs"

dotenv.config()

const FEATURE_R_ANALYTICS = String(process.env.FEATURE_R_ANALYTICS || "false") === "true"
const ON_WRITE_ANALYTICS = String(process.env.FEATURE_BP_ON_WRITE || "true") === "true"
const ROLLOUT_MODE = (process.env.BP_ROLLOUT_MODE || "all").toLowerCase()
const CANARY_IMEIS = (process.env.BP_CANARY_IMEIS || "")
  .split(",")
  .map(v => v.trim())
  .filter(v => v.length > 0)
const DEBOUNCE_MS = parseInt(process.env.BP_ON_WRITE_DEBOUNCE_MS || "15000", 10)

const emitter = new EventEmitter()
const pendingTimers = new Map()

const SUPPORTED_DEVICE_TYPES = ["ad", "transtek", "bodytrace", "withings"]

function scheduleProcessing(imei, deviceType) {
  if (
    ROLLOUT_MODE === "canary" &&
    CANARY_IMEIS.length > 0 &&
    !CANARY_IMEIS.includes(String(imei))
  ) {
    return
  }
  if (pendingTimers.has(imei)) {
    clearTimeout(pendingTimers.get(imei))
  }
  const timer = setTimeout(async () => {
    pendingTimers.delete(imei)
    try {
      if (!ON_WRITE_ANALYTICS) return
      await processAllDurations(imei)
    } catch (err) {
      console.error(
        `[BP] On-write processing failed for IMEI ${imei} (${deviceType}):`,
        err.message,
      )
    }
  }, DEBOUNCE_MS)
  pendingTimers.set(imei, timer)
}

export function emitNewBPReading(
  imei,
  manufacturer = "unknown",
  sourceCollection = "unknown",
  timestamp = new Date().toISOString(),
) {
  try {
    if (!SUPPORTED_DEVICE_TYPES.includes(manufacturer)) {
      console.warn(`[BP] Unsupported device type: ${manufacturer} for IMEI ${imei}`)
    }
    emitter.emit("bp:new", { imei, manufacturer, sourceCollection, timestamp })
  } catch (err) {
    console.error("[BP] Failed to emit new reading event:", err.message)
  }
}

emitter.on("bp:new", evt => {
  if (!evt || !evt.imei) return
  scheduleProcessing(evt.imei, evt.manufacturer)
})

export default { emitNewBPReading }
