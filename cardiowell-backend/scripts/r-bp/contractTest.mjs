import dotenv from "dotenv"
import * as rClient from "../../utils/rAnalyticsClient.mjs"

dotenv.config()

function assert(condition, message) {
  if (!condition) throw new Error(message)
}

function logPass(message) {
  console.log(`[PASS] ${message}`)
}

function logFail(message, err) {
  console.error(`[FAIL] ${message}: ${err?.message || err}`)
}

function hasKeys(obj, keys) {
  return keys.every(k => Object.prototype.hasOwnProperty.call(obj, k))
}

async function testHealthAndVersion() {
  const health = await rClient.health()
  assert(health && (health.status === "ok" || health.status === "error"), "health shape")
  logPass("health endpoint reachable")

  const version = await rClient.version()
  assert("version" in version, "version field present")
  logPass("version endpoint reachable")
}

async function testComputeDurations() {
  const samplePatient = "patient_TEST_IMEI"
  const windows = ["1Month", "3Months"]
  const res = await rClient.computeDurations(samplePatient, windows)
  assert(
    res && hasKeys(res, ["patientId", "windows", "metrics"]),
    "compute-durations response shape",
  )
  for (const w of windows) {
    assert(res.metrics[w], `metrics exist for ${w}`)
  }
  logPass("compute-durations contract OK")
}

export async function runContractTests() {
  const results = { passed: 0, failed: 0 }
  try {
    await testHealthAndVersion()
    results.passed += 1
  } catch (err) {
    results.failed += 1
    logFail("health/version", err)
  }
  try {
    await testComputeDurations()
    results.passed += 1
  } catch (err) {
    results.failed += 1
    logFail("compute-durations", err)
  }
  console.log(
    `Contract tests complete: ${results.passed} passed, ${results.failed} failed`,
  )
  if (results.failed > 0) process.exitCode = 1
}

if (import.meta.url === `file://${process.argv[1]}`) {
  runContractTests()
}
