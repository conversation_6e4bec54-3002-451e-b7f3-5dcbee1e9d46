import { getCardiowellDB } from "./mongoConnection.mjs"
import { createHash } from "crypto"

const deviceCache = new Map()

const validateBPReading = (sbp, dbp, pulse) => {
  if (sbp < 70 || sbp > 260) return false
  if (dbp < 40 || dbp > 140) return false
  if (dbp >= sbp) return false
  if (pulse && (pulse < 30 || pulse > 200)) return false
  return true
}

const detectOutliers = readings => {
  if (readings.length < 3) return readings

  const sbpValues = readings.map(r => r.sbp).filter(v => v !== null)
  const dbpValues = readings.map(r => r.dbp).filter(v => v !== null)

  const sbpMean = sbpValues.reduce((a, b) => a + b, 0) / sbpValues.length
  const dbpMean = dbpValues.reduce((a, b) => a + b, 0) / dbpValues.length

  const sbpStd = Math.sqrt(
    sbpValues.reduce((sq, n) => sq + Math.pow(n - sbpMean, 2), 0) / sbpValues.length,
  )
  const dbpStd = Math.sqrt(
    dbpValues.reduce((sq, n) => sq + Math.pow(n - dbpMean, 2), 0) / dbpValues.length,
  )

  return readings.filter(reading => {
    const sbpZ = Math.abs((reading.sbp - sbpMean) / sbpStd)
    const dbpZ = Math.abs((reading.dbp - dbpMean) / dbpStd)
    return sbpZ < 3 && dbpZ < 3
  })
}

const deduplicateReadings = readings => {
  const seen = new Set()
  return readings.filter(reading => {
    const key = `${reading.readingAtUTC}:${reading.sbp}:${reading.dbp}:${reading.pulse || ""}`
    if (seen.has(key)) return false
    seen.add(key)
    return true
  })
}

const normalizeTimestamp = (timestamp, deviceType) => {
  if (!timestamp) return null

  if (deviceType === "transtek" || deviceType === "withings") {
    if (typeof timestamp === "number") {
      return timestamp > 1e12 ? new Date(timestamp) : new Date(timestamp * 1000)
    }
    return new Date(timestamp)
  }

  if (typeof timestamp === "string") {
    return new Date(timestamp)
  }

  if (typeof timestamp === "number") {
    return timestamp > 1e12 ? new Date(timestamp) : new Date(timestamp * 1000)
  }

  return new Date(timestamp)
}

const normalizeBPValue = (value, deviceType) => {
  if (value === null || value === undefined) return null

  if (deviceType === "withings" && typeof value === "object") {
    return value.value || null
  }

  const numValue = parseInt(value)
  return isNaN(numValue) ? null : numValue
}

const normalizeReading = (reading, deviceType, imei) => {
  let sbp,
    dbp,
    pulse,
    timestamp,
    source,
    isTest = false

  if (deviceType === "ad") {
    const payload = reading.payload
    if (!payload.sys || !payload.dia || !payload.timestamp) return null
    sbp = parseInt(payload.sys)
    dbp = parseInt(payload.dia)
    pulse = payload.pulse ? parseInt(payload.pulse) : null
    timestamp = new Date(payload.timestamp)
    source = "ad_bpms"
    isTest = payload.isTest || false
  } else if (deviceType === "transtek") {
    if (!reading.systolic || !reading.diastolic || !reading.ts) return null
    sbp = parseInt(reading.systolic)
    dbp = parseInt(reading.diastolic)
    pulse = reading.pulse ? parseInt(reading.pulse) : null
    timestamp = normalizeTimestamp(reading.ts, deviceType)
    source = "Transtek_BPM"
    isTest = reading.isTest || false
  } else if (deviceType === "bodytrace") {
    if (reading._collection === "bodytracemessages") {
      const message = reading.message
      if (!message?.values?.systolic || !message?.values?.diastolic || !reading.createdAt)
        return null
      sbp = parseInt(message.values.systolic) / 100
      dbp = parseInt(message.values.diastolic) / 100
      pulse = message.values.pulse ? parseInt(message.values.pulse) : null
      timestamp = new Date(reading.createdAt)
      source = "bodytracemessages"
    } else {
      return null
    }
  } else if (deviceType === "withings") {
    if (!reading.sys?.value || !reading.dia?.value || !reading.created) return null
    sbp = normalizeBPValue(reading.sys, deviceType)
    dbp = normalizeBPValue(reading.dia, deviceType)
    pulse = reading.pulse ? normalizeBPValue(reading.pulse, deviceType) : null
    timestamp = normalizeTimestamp(reading.created, deviceType)
    source = "withingsBloodPressureData"
  }

  if (!validateBPReading(sbp, dbp, pulse)) return null

  return {
    patientId: `patient_${imei}`,
    readingAtUTC: timestamp.toISOString(),
    sbp,
    dbp,
    pulse,
    deviceType,
    deviceId: imei,
    source,
    sourceReadingId: reading._id.toString(),
    isTest,
  }
}

const detectDeviceType = async (db, imei) => {
  if (deviceCache.has(imei)) {
    return deviceCache.get(imei)
  }

  const checks = [
    { type: "ad", collection: "ad_bpms", query: { "payload.imei": imei } },
    { type: "transtek", collection: "Transtek_BPM", query: { imei } },
    {
      type: "bodytrace",
      collection: "bodytracemessages",
      query: { $expr: { $eq: ["$message.imei", imei] } },
    },
    {
      type: "withings",
      collection: "withingsBloodPressureData",
      query: { deviceId: imei },
    },
  ]

  for (const check of checks) {
    try {
      const count = await db.collection(check.collection).countDocuments(check.query)
      if (count > 0) {
        const result = { type: check.type, collection: check.collection }
        deviceCache.set(imei, result)
        return result
      }
    } catch (error) {
      continue
    }
  }

  const result = { type: "ad", collection: "ad_bpms" }
  deviceCache.set(imei, result)
  return result
}

export const fetchBPReadings = async (imei, startDate = null, endDate = null) => {
  try {
    const db = await getCardiowellDB()
    const deviceInfo = await detectDeviceType(db, imei)

    let allReadings = []

    try {
      const collection = db.collection(deviceInfo.collection)
      const query =
        deviceInfo.type === "ad"
          ? { "payload.imei": imei }
          : deviceInfo.type === "transtek"
            ? { imei }
            : deviceInfo.type === "bodytrace"
              ? { "message.imei": imei }
              : { deviceId: imei }

      if (startDate && endDate) {
        const startIso = startDate + "T00:00:00.000Z"
        const endIso = endDate + "T23:59:59.999Z"
        const startMs = new Date(startIso).getTime()
        const endMs = new Date(endIso).getTime()

        if (deviceInfo.type === "bodytrace") {
          query.createdAt = { $gte: new Date(startIso), $lte: new Date(endIso) }
        } else {
          const timestampField =
            deviceInfo.type === "ad"
              ? "payload.timestamp"
              : deviceInfo.type === "transtek"
                ? "ts"
                : "created"

          query.$or = [
            { [timestampField]: { $gte: startIso, $lte: endIso } },
            { [timestampField]: { $gte: startMs, $lte: endMs } },
          ]
        }
      }

      const sortField =
        deviceInfo.type === "ad"
          ? "payload.timestamp"
          : deviceInfo.type === "transtek"
            ? "ts"
            : deviceInfo.type === "bodytrace"
              ? "createdAt"
              : "created"

      const readings = await collection
        .find(query)
        .sort({ [sortField]: 1 })
        .limit(10000)
        .toArray()
      allReadings = readings.map(r => ({ ...r, _collection: deviceInfo.collection }))
    } catch (collectionError) {
      console.warn(
        `Failed to fetch from collection ${deviceInfo.collection}:`,
        collectionError.message,
      )
      return []
    }

    if (allReadings.length === 0) {
      return []
    }

    const normalizedReadings = allReadings
      .map(reading => normalizeReading(reading, deviceInfo.type, imei))
      .filter(reading => reading !== null)

    const deduplicatedReadings = deduplicateReadings(normalizedReadings)
    const qualityFilteredReadings = detectOutliers(deduplicatedReadings)

    return qualityFilteredReadings
  } catch (error) {
    console.error("Error fetching BP readings:", error)
    throw error
  }
}

export const getDayRange = day => {
  const start = new Date(day + "T00:00:00.000Z")
  const end = new Date(day + "T23:59:59.999Z")
  return { start, end }
}

export const groupReadingsByDay = readings => {
  const grouped = {}
  readings.forEach(reading => {
    const day = reading.readingAtUTC.split("T")[0]
    if (!grouped[day]) grouped[day] = []
    grouped[day].push(reading)
  })
  return grouped
}

export const getAvailableDays = async imei => {
  try {
    const db = await getCardiowellDB()
    const deviceInfo = await detectDeviceType(db, imei)

    let allReadings = []

    try {
      const collection = db.collection(deviceInfo.collection)
      const query =
        deviceInfo.type === "ad"
          ? { "payload.imei": imei }
          : deviceInfo.type === "transtek"
            ? { imei }
            : deviceInfo.type === "bodytrace"
              ? { $expr: { $eq: ["$message.imei", imei] } }
              : { deviceId: imei }

      const readings = await collection.find(query).toArray()
      allReadings = readings.map(r => ({ ...r, _collection: deviceInfo.collection }))
    } catch (collectionError) {
      console.warn(
        `Failed to fetch from collection ${deviceInfo.collection}:`,
        collectionError.message,
      )
      return []
    }

    const dayCounts = {}

    allReadings.forEach(reading => {
      let timestamp, day

      if (deviceInfo.type === "ad") {
        timestamp = reading.payload.timestamp
      } else if (deviceInfo.type === "transtek") {
        timestamp = reading.ts
      } else if (deviceInfo.type === "bodytrace") {
        if (reading._collection === "bodytracemessages") {
          timestamp = reading.createdAt
        } else {
          timestamp = reading._created_at
        }
      } else if (deviceInfo.type === "withings") {
        timestamp = reading.created
      }

      if (typeof timestamp === "string") {
        day = timestamp.split("T")[0]
      } else if (timestamp instanceof Date) {
        day = timestamp.toISOString().split("T")[0]
      } else {
        const date = new Date(timestamp)
        day = date.toISOString().split("T")[0]
      }
      dayCounts[day] = (dayCounts[day] || 0) + 1
    })

    const result = Object.keys(dayCounts)
      .sort()
      .map(day => ({ day, count: dayCounts[day] }))
    return result
  } catch (error) {
    console.error("Error getting available days:", error)
    throw error
  }
}
