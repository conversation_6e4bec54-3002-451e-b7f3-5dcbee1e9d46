import axios from "axios"
import crypto from "crypto"
import dotenv from "dotenv"

dotenv.config()

// Environment configuration (R analytics)
const BASE_URL = process.env.R_ANALYTICS_BASE_URL
const TOKEN = process.env.R_ANALYTICS_TOKEN || ""
const TIMEOUT_MS = parseInt(process.env.R_ANALYTICS_TIMEOUT_MS || "15000", 10)
const RETRY_COUNT = parseInt(process.env.R_ANALYTICS_RETRY_COUNT || "2", 10)
const RETRY_BACKOFF_MS = parseInt(process.env.R_ANALYTICS_RETRY_BACKOFF_MS || "500", 10)
const CIRCUIT_OPEN_THRESHOLD = parseInt(
  process.env.R_ANALYTICS_CIRCUIT_OPEN_THRESHOLD || "5",
  10,
)
const CIRCUIT_RESET_MS = parseInt(process.env.R_ANALYTICS_CIRCUIT_RESET_MS || "60000", 10)
const CORRELATION_HEADER = process.env.REQUEST_CORRELATION_HEADER || "X-Request-ID"

const circuit = {
  state: "closed",
  failures: 0,
  openedAt: null,
}

function nowMs() {
  return Date.now()
}

function shouldAllowRequest() {
  if (circuit.state === "closed") return true
  if (circuit.state === "open") {
    const elapsed = nowMs() - (circuit.openedAt || 0)
    if (elapsed >= CIRCUIT_RESET_MS) {
      circuit.state = "half_open"
      return true
    }
    return false
  }
  if (circuit.state === "half_open") return true
  return true
}

function onSuccess() {
  circuit.failures = 0
  circuit.state = "closed"
  circuit.openedAt = null
}

function onFailure() {
  circuit.failures += 1
  if (circuit.failures >= CIRCUIT_OPEN_THRESHOLD) {
    circuit.state = "open"
    circuit.openedAt = nowMs()
  }
}

function generateCorrelationId() {
  return crypto.randomUUID ? crypto.randomUUID() : crypto.randomBytes(16).toString("hex")
}

function buildHeaders(extraHeaders = {}) {
  const headers = {
    "Content-Type": "application/json",
    [CORRELATION_HEADER]: generateCorrelationId(),
    "x-service": "cardiowell-backend",
    ...extraHeaders,
  }
  if (TOKEN) headers["Authorization"] = `Bearer ${TOKEN}`
  return headers
}

async function postJson(path, payload) {
  if (!shouldAllowRequest()) {
    const err = new Error("R analytics circuit is open")
    err.code = "CIRCUIT_OPEN"
    throw err
  }

  const url = `${BASE_URL}${path}`
  let lastError = null

  for (let attempt = 0; attempt <= RETRY_COUNT; attempt += 1) {
    try {
      const response = await axios.post(url, payload, {
        headers: buildHeaders(),
        timeout: TIMEOUT_MS,
        validateStatus: s => s >= 200 && s < 500,
      })

      if (response.status >= 200 && response.status < 300) {
        onSuccess()
        return response.data
      }

      lastError = new Error(
        `R analytics ${path} responded with status ${response.status}`,
      )
    } catch (err) {
      lastError = err
    }

    if (attempt < RETRY_COUNT) {
      const delay = Math.max(RETRY_BACKOFF_MS * Math.pow(2, attempt), RETRY_BACKOFF_MS)
      await new Promise(r => setTimeout(r, delay))
    }
  }

  onFailure()
  throw lastError
}

export async function health() {
  try {
    const res = await axios.get(`${BASE_URL}/v1/health`, {
      headers: buildHeaders(),
      timeout: 5000,
    })
    return res.data
  } catch (e) {
    return { status: "error", error: e.message }
  }
}

export async function version() {
  try {
    const res = await axios.get(`${BASE_URL}/v1/version`, {
      headers: buildHeaders(),
      timeout: 5000,
    })
    return res.data
  } catch (e) {
    return { version: null, error: e.message }
  }
}

export async function computeDay(payload) {
  return postJson("/v1/bp/compute/day", payload)
}

export async function computeDuration(payload) {
  return postJson("/v1/bp/compute/duration", payload)
}

export async function computeDurations(patientId, windows) {
  const payload = { patientId, windows }
  return postJson("/v1/bp/compute-durations", payload)
}

export default {
  health,
  version,
  computeDay,
  computeDuration,
  computeDurations,
}
