import { getMeas } from "./getMeas.mjs"
import { bpmAppli } from "../utils/categories.mjs"
import { saveBloodPressureData } from "./saveBloodPressureData.mjs"
import { getPatientDataById } from "../../users/service/patientData.mjs"
import withingsUserData from "../../models/withingsUserData.js"
import { getAccessToken } from "./getAccessToken.mjs"
import { handleWithingsMessage } from "../../device/service/withings/handleWithingsMessage.mjs"
import { emitNewBPReading } from "../../scripts/r-bp/bpEventSystem.mjs"

export const onNotification = async ({
  clientId,
  clientSecret,
  socket,
  callbackUrl,
  appli,
  userId,
  startDate,
  endDate,
}) => {
  const userData = await withingsUserData.findOne({ userId })
  if (!userData) {
    console.error("User not found")
    return
  }
  console.log(
    `appli: ${appli}, user id: ${userId}, lastUpdated: ${userData.lastUpdated}, start date: ${startDate}, end date: ${endDate}`,
  )
  if (userData.lastUpdated >= endDate) {
    console.error("Already received updates for these readings")
    return
  }

  const accessToken = await getAccessToken({
    clientId,
    clientSecret,
    userData,
    userId,
    callbackUrl,
  })
  if (!accessToken) {
    return
  }

  // get measurement data
  const bodyArray = await getMeas({
    accessToken,
    appli,
    startDate,
    endDate,
  })

  let newData = false
  if (parseInt(appli) === bpmAppli) {
    newData = await saveBloodPressureData(bodyArray)
    if (newData && newData.length > 0) {
      const imei = userData.patientId || userId
      emitNewBPReading(
        imei,
        "withings",
        "withingsBloodPressureData",
        new Date().toISOString(),
      )
    }
  }

  if (newData) {
    // Notify frontend there is a data update
    await Promise.all(newData?.map(message => handleWithingsMessage(message)))

    const patientId = userData.patientId
    getPatientDataById(patientId).then(patient => {
      const clinic = patient?.patientObj?.clinic
      clinic && socket.emit("withingsDataUpdate", { patientId, clinic })
    })
  }

  // update lastUpdated
  withingsUserData
    .findOneAndUpdate({ userId }, { lastUpdated: endDate })
    .catch(error => console.error(error))

  return
}
