// External Libraries
import { useState, useEffect } from 'react'
import { Container, CssBase<PERSON>, Button, Box, Typography } from '@mui/material'

// Styles
import { useFormStyles } from '../../common/style'

// Internal Components
import { ConfirmationPage } from './ConfirmationPage'
import { DeviceRegisterForm } from './DeviceRegisterForm'
import {
  NotificationModal,
  NOTIFICATION_TYPES,
} from '../../common/modals/NotificationModal'

// Services
import { getDeviceStatus } from '../getDeviceStatus'
import { registerDeviceUpdates } from '../registerDeviceUpdates'
import { resetDeviceRegistration } from '../resetDeviceRegistration'

// Utilities
import { history } from '../../../App'

const alreadyActivatedMessage =
  'Device has already been activated to receive notifications'

export const DeviceSignUp = (props) => {
  const imei = props.match.params.imei
  const classes = useFormStyles()
  const [showForm, setShowForm] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)

  //TODO: need to display somewhere serverMessage
  const [serverMessage, setServerMessage] = useState('')
  const [isPending, setIsPending] = useState(false)
  const [openErrorModal, setOpenErrorModal] = useState(false)
  const [isResetPending, setIsResetPending] = useState(false)
  const [showResetSuccess, setShowResetSuccess] = useState(false)

  const onSubmit = (formData) => {
    setServerMessage('')
    setIsPending(true)
    registerDeviceUpdates({ ...formData, imei })
      .then((data) => {
        if (data.error) {
          setServerMessage(data.message)
          setOpenErrorModal(true)
        } else {
          setShowForm(false)
          setServerMessage('')
          setShowConfirmation(true)
        }
      })
      .catch((error) => {
        setServerMessage(`An error occurred : ${error}`)
        setOpenErrorModal(true)
      })
      .finally(() => {
        setIsPending(false)
      })
  }

  const handleCloseErrorModal = () => {
    setOpenErrorModal(false)
  }

  const handleResetRegistration = async () => {
    setIsResetPending(true)
    setShowResetSuccess(false)
    try {
      const data = await resetDeviceRegistration(imei)
      if (data.error) {
        setServerMessage(data.message)
        setOpenErrorModal(true)
      } else {
        setShowResetSuccess(true)
        // Clear localStorage for patient form completion and avatar
        localStorage.removeItem('patientFormCompleted')
        localStorage.removeItem(`avatar_${imei}`)
        // Refresh device status after reset
        setTimeout(() => {
          window.location.reload()
        }, 2000)
      }
    } catch (error) {
      setServerMessage(`Reset failed: ${error.message}`)
      setOpenErrorModal(true)
    } finally {
      setIsResetPending(false)
    }
  }

  useEffect(() => {
    getDeviceStatus(imei).then((data) => {
      if (data.activated) {
        setShowForm(false)
        setServerMessage(alreadyActivatedMessage)
        history.push(`/device-updates/dashboard/${imei}`)
      } else {
        setShowForm(true)
        setServerMessage('')
      }
    })
  }, [imei])

  return (
    <Container component="main" maxWidth="xs">
      <CssBaseline />
      <div
        className={classes.paper}
        style={{ marginTop: showConfirmation ? 0 : undefined }}
      >
        {showForm ? (
          <DeviceRegisterForm
            imei={imei}
            onSubmit={onSubmit}
            isPending={isPending}
          />
        ) : showConfirmation ? (
          <ConfirmationPage imei={imei} match={{ params: { imei: imei } }} />
        ) : null}

        {/* Dev Reset Button for unregistered devices */}
        {process.env.NODE_ENV === 'development' && showForm && (
          <Box sx={{ mt: 2 }}>
            <Button
              fullWidth
              variant="outlined"
              color="secondary"
              onClick={handleResetRegistration}
              disabled={isResetPending}
              sx={{
                borderColor: '#d32f2f',
                color: '#d32f2f',
                '&:hover': {
                  borderColor: '#b71c1c',
                  backgroundColor: 'rgba(211, 47, 47, 0.04)',
                },
              }}
            >
              {isResetPending ? 'Resetting...' : 'Reset Registration (DEV)'}
            </Button>
          </Box>
        )}

        {/* Success message for reset */}
        {showResetSuccess && (
          <Box
            sx={{ mt: 2, p: 2, backgroundColor: '#e8f5e8', borderRadius: 1 }}
          >
            <Typography align="center" sx={{ color: '#2e7d32' }}>
              Registration reset successfully! Refreshing page...
            </Typography>
          </Box>
        )}

        <NotificationModal
          open={openErrorModal}
          onClose={handleCloseErrorModal}
          title="Your data was not saved."
          message="Please try again."
          type={NOTIFICATION_TYPES.ERROR}
          autoCloseTime={5000}
          position="bottom"
        />
      </div>
    </Container>
  )
}
