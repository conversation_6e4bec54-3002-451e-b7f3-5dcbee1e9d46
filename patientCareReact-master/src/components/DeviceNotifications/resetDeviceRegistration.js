/**
 * Reset device registration for development environment
 * This allows restarting the registration process from the beginning
 */
export const resetDeviceRegistration = async (imei) => {
  return fetch(`/routes/device-updates/${imei}/reset`, {
    withCredentials: true,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
  }).then((response) => {
    if (response.ok) {
      return response.json()
    }
    if (response.status === 403) {
      return {
        error: 'forbidden',
        message: 'Reset endpoint is only available in development environment',
      }
    }
    if (response.status === 404) {
      return {
        error: 'imei-account-not-found',
        message: 'This IMEI does not exist in any account in our records',
      }
    }
    return {
      error: 'server-error',
      message: 'An error occurred while resetting device registration',
    }
  })
}
