export async function getBpDurationMetrics(imei) {
  if (!imei) return null

  const url = `/api/bp-duration-metrics/${encodeURIComponent(imei)}`
  const res = await fetch(url, { method: 'GET', credentials: 'include' })
  if (!res.ok) {
    console.error(`BP duration metrics fetch failed: ${res.status}`)
    throw new Error('Unable to load BP duration data')
  }
  const data = await res.json()
  return data
}
