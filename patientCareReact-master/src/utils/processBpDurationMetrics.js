export function extractDurationAverages(data) {
  if (!data?.durationAverages) return null

  return {
    last7Days: data.durationAverages.last7Days || null,
    last1Month: data.durationAverages.last1Month || null,
    last3Months: data.durationAverages.last3Months || null,
    last1Year: data.durationAverages.last1Year || null,
    allTime: data.durationAverages.allTime || null,
  }
}

export function extractTimeOfDayAverages(data) {
  if (!data?.timeOfDayAverages) return null

  return {
    morning: data.timeOfDayAverages.morning || null,
    afternoon: data.timeOfDayAverages.afternoon || null,
    evening: data.timeOfDayAverages.evening || null,
    night: data.timeOfDayAverages.night || null,
  }
}

export function extractMinMaxValues(data) {
  if (!data?.extremeValues) return null

  return {
    maxBP: data.extremeValues.maxBP || null,
    minBP: data.extremeValues.minBP || null,
  }
}

export function formatBpDisplay(systolic, diastolic) {
  if (!systolic || !diastolic) return null
  return `${systolic}/${diastolic}`
}

export function getAllMetrics(data) {
  if (!data) return null

  return {
    durationAverages: extractDurationAverages(data),
    timeOfDayAverages: extractTimeOfDayAverages(data),
    extremeValues: extractMinMaxValues(data),
  }
}

export function getDurationBasedMetrics(data) {
  const durationAverages = extractDurationAverages(data)
  if (!durationAverages) return []

  return [
    { title: 'Last 7 Days Avg BP', value: durationAverages.last7Days || '-' },
    { title: 'Last 1 Month Avg BP', value: durationAverages.last1Month || '-' },
    {
      title: 'Last 3 Months Avg BP',
      value: durationAverages.last3Months || '-',
    },
    { title: 'Last 1 Year Avg BP', value: durationAverages.last1Year || '-' },
    { title: 'All Time Avg BP', value: durationAverages.allTime || '-' },
  ]
}

export function getTimeOfDayAndExtremeMetrics(data) {
  const timeOfDayAverages = extractTimeOfDayAverages(data)
  const extremeValues = extractMinMaxValues(data)

  const metrics = []

  if (timeOfDayAverages) {
    metrics.push(
      { title: 'Morning Avg BP', value: timeOfDayAverages.morning || '-' },
      { title: 'Afternoon Avg BP', value: timeOfDayAverages.afternoon || '-' },
      { title: 'Evening Avg BP', value: timeOfDayAverages.evening || '-' },
      { title: 'Night Avg BP', value: timeOfDayAverages.night || '-' }
    )
  }

  if (extremeValues) {
    metrics.push(
      { title: 'Max Blood Pressure', value: extremeValues.maxBP || '-' },
      { title: 'Min Blood Pressure', value: extremeValues.minBP || '-' }
    )
  }

  return metrics
}
